import aiohttp
import asyncio
import json
import base64
import platform
import concurrent.futures
import os
import threading

# 添加文件写入锁，确保线程安全
file_lock = threading.Lock()
err_lock = threading.Lock()  # 错误文件的锁

# 添加结果缓存和计数器
results_cache = []
cache_lock = threading.Lock()
results_count = 0

async def query_hukou(session, card_id):
    """
    查询户口簿信息
    """
    url = 'https://zwfw.dn.haikou.gov.cn/customer/rest/provinces/data/open/getSign_HNSDZZZ_info'
    
    data = {
        "cardid": card_id,
        "type": "户口簿"
    }
    
    try:
        # 添加5秒超时
        async with session.post(url, json=data, timeout=5) as response:
            result = await response.json()
            if result['code'] == '1' and result['resultDatas']['data']:
                return result['resultDatas']['data'][0]
            return None
    except asyncio.TimeoutError:
        # 超时处理
        print(f"[{card_id}] 户口簿查询超时")
        return None
    except Exception as e:
        print(f"[{card_id}] 户口簿查询异常: {str(e)}")
        return None

async def query_id_card(card_id, cert_name="居民身份证", verbose=True):
    """
    查询身份证和户口簿信息
    :param card_id: 身份证号码
    :param cert_name: 证件类型
    :param verbose: 是否输出详细信息
    :return: 解析后的结果
    """
    url = 'https://zwfw.dn.haikou.gov.cn/customer/rest/zzk/provincElecEctResultQuery'

    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0',
        'Content-Type': 'application/json'
    }

    params = {
        'fileType': 'json',
        'imageField': 'IMAGE'
    }

    data = {
        'cardid': card_id,
        'certName': cert_name
    }

    session = None
    try:
        # 创建有超时设置的会话
        timeout = aiohttp.ClientTimeout(total=5)  # 5秒总超时
        session = aiohttp.ClientSession(timeout=timeout)

        # 并行查询身份证和户口簿信息
        id_card_task = session.post(url, params=params, headers=headers, json=data)
        hukou_task = query_hukou(session, card_id)

        # 等待两个查询都完成
        id_card_response, hukou_data = await asyncio.gather(
            id_card_task,
            hukou_task,
            return_exceptions=True  # 返回异常而不是抛出
        )

        # 检查是否有异常
        if isinstance(id_card_response, Exception):
            raise id_card_response

        # 处理身份证信息
        result = await id_card_response.json()
            
            # 创建一个包含所有数据的字典
            all_data = {}
            
            if result['code'] == '1':  # 成功状态码
                data = result['resultDatas']
                cert_info = data['dzzzValidInfo']
                cert_value_map = data['certValueMapList'][0]
                
                # 获取照片数据
                photo_data = None
                original_base64 = None
                if 'IMAGE' in cert_value_map:
                    base64_data = cert_value_map['IMAGE']['customValue']
                    # 保存原始base64字符串
                    original_base64 = base64_data
                    if ',' in base64_data:
                        base64_data = base64_data.split(',')[1]
                        original_base64 = base64_data
                    # 不再解码为图片数据
                
                # 收集身份证信息到字典
                id_info = {
                    "姓名": cert_info['CZZT'],
                    "身份证号": cert_info['ZZBH'],
                    "性别": cert_value_map['DICXB']['customValue'],
                    "民族": cert_value_map['DICMC']['customValue'],
                    "出生日期": cert_value_map['CSCS']['customValue'],
                    "住址": cert_value_map['ZZZZ']['customValue'],
                    "签发机关": cert_info['ZZBFJG'],
                    "签发机关代码": cert_info.get('ZZBFJGDM', '未知'),
                    "有效期限": f"{cert_value_map['ZZYXQQSRQ']['customValue']} - {cert_value_map['ZZYXQJZRQ']['customValue']}",
                    "证照标识": cert_info.get('ZZBS', '未知'),
                    "证照状态": cert_info.get('ZZZT', '未知'),
                    "证照类型代码": cert_info.get('ZZLXDM', '未知'),
                    "持证主体代码类型": cert_info.get('CZZTDMLXDM', '未知')
                }
                
                # 添加到总数据字典
                all_data["身份证信息"] = id_info
                
                # 仅在详细模式下打印信息
                if verbose:
                    id_print_info = "\n=== 身份证信息 ===\n"
                    for key, value in id_info.items():
                        id_print_info += f"{key}：{value}\n"
                    print(f"[{card_id}] {id_print_info}")
                
                # 收集户口簿信息
                if hukou_data and not isinstance(hukou_data, Exception):
                    hukou_info = {
                        "姓名": hukou_data.get('xm', '未知'),
                        "性别": hukou_data.get('xb', '未知'),
                        "民族": hukou_data.get('mz', '未知'),
                        "出生日期": hukou_data.get('csrq', '未知'),
                        "出生地": hukou_data.get('csdssxq', '未知'),
                        "籍贯": hukou_data.get('jg', '未知'),
                        "身份证号": hukou_data.get('gmsfhm', '未知'),
                        "户籍地址": hukou_data.get('hjdz', '未知'),
                        "行政区划代码": hukou_data.get('lhxzqhdm', '未知'),
                        "行政区划": hukou_data.get('lhxzqhmc', '未知'),
                        "派出所代码": hukou_data.get('pcsdm', '未知'),
                        "派出所名称": hukou_data.get('pcsmc', '未知'),
                        "街道/镇代码": hukou_data.get('xzjddm', '未知'),
                        "街道/镇名称": hukou_data.get('xzjdmc', '未知'),
                        "社区/居委会代码": hukou_data.get('sqjcwhdm', '未知'),
                        "社区/居委会名称": hukou_data.get('sqjcwhmc', '未知'),
                        "数据更新时间": hukou_data.get('ods_gxsj', '未知')
                    }
                    
                    # 添加到总数据字典
                    all_data["户口簿信息"] = hukou_info
                    
                    # 仅在详细模式下打印信息
                    if verbose:
                        hukou_print_info = "\n=== 户口簿信息 ===\n"
                        for key, value in hukou_info.items():
                            hukou_print_info += f"{key}：{value}\n"
                        print(f"[{card_id}] {hukou_print_info}")
                
                # 收集照片信息
                if original_base64:
                    # 不再保存JPG文件，只保存base64数据
                    
                    # 在保存到JSON前清除base64中的换行符和空白字符
                    # 这样在JSON序列化后仍然是有效的base64字符串
                    clean_base64 = original_base64.replace('\r', '').replace('\n', '').replace(' ', '')
                    
                    # 添加照片信息到总数据字典
                    all_data["照片信息"] = {
                        "照片Base64数据": clean_base64
                    }
                    
                    # 仅在详细模式下打印信息
                    if verbose:
                        print(f"[{card_id}] \n=== 照片信息 ===\n照片数据已保存在结果中\n")
                elif verbose:
                    print(f"[{card_id}] \n未获取到照片数据\n")
                
                if verbose:
                    print(f"[{card_id}] ================\n")
                
                # 将结果添加到缓存，不直接写入文件
                with cache_lock:
                    global results_cache, results_count
                    results_cache.append(json.dumps(all_data, ensure_ascii=False))
                    results_count += 1
                    
                    # 每100条结果保存一次
                    if results_count % 100 == 0:
                        with file_lock:
                            with open('Okey.txt', 'a', encoding='utf-8') as f:
                                for result_json in results_cache:
                                    f.write(result_json + "\n")
                            # 清空缓存
                            results_cache = []
                
                if verbose:
                    print(f"[{card_id}] 查询结果已添加到缓存")
                return True

            else:
                if verbose:
                    error_message = f"\n查询失败: {result.get('title', '未知错误')}\n"
                    print(f"[{card_id}] {error_message}")
                # 将错误信息写入err.txt文件
                with err_lock:
                    with open('err.txt', 'a', encoding='utf-8') as f:
                        f.write(json.dumps({"错误": result.get('title', '未知错误'), "身份证号": card_id}, ensure_ascii=False) + "\n")
                return False

    except asyncio.TimeoutError:
        error_message = "\n查询超时（超过5秒）\n"
        if verbose:
            print(f"[{card_id}] {error_message}")
        # 将错误信息写入err.txt文件
        with err_lock:
            with open('err.txt', 'a', encoding='utf-8') as f:
                f.write(json.dumps({"错误": "查询超时（超过5秒）", "身份证号": card_id}, ensure_ascii=False) + "\n")
        return False
    except aiohttp.ClientError as e:
        if verbose:
            error_message = f"\n请求异常: {str(e)}\n"
            print(f"[{card_id}] {error_message}")
        # 将错误信息写入err.txt文件
        with err_lock:
            with open('err.txt', 'a', encoding='utf-8') as f:
                f.write(json.dumps({"错误": f"请求异常: {str(e)}", "身份证号": card_id}, ensure_ascii=False) + "\n")
        return False
    except Exception as e:
        if verbose:
            error_message = f"\n处理异常: {str(e)}\n"
            print(f"[{card_id}] {error_message}")
        # 将错误信息写入err.txt文件
        with err_lock:
            with open('err.txt', 'a', encoding='utf-8') as f:
                f.write(json.dumps({"错误": f"处理异常: {str(e)}", "身份证号": card_id}, ensure_ascii=False) + "\n")
        return False
    finally:
        # 确保会话被正确关闭
        if session and not session.closed:
            try:
                await session.close()
            except Exception:
                pass  # 忽略关闭会话时的异常

# 新增函数：从文件读取身份证号
def read_card_ids(file_path):
    """
    从文件中读取身份证号，每行一个
    :param file_path: 文件路径
    :return: 身份证号列表
    """
    card_ids = []
    try:
        with open(file_path, 'r') as f:
            for line in f:
                # 去除空白字符并添加到列表
                card_id = line.strip()
                if card_id:  # 忽略空行
                    card_ids.append(card_id)
        
        print(f"已读{len(card_ids)}条")
        return card_ids
    except Exception as e:
        print(f"读取身份证号文件时出错: {str(e)}")
        return []

# 新增函数：处理单个身份证号的线程函数
def process_card_id(card_id):
    """
    作为线程目标函数处理单个身份证号
    :param card_id: 身份证号码
    :return: 处理结果
    """
    loop = None
    try:
        # 为Windows平台设置事件循环策略
        if platform.system() == 'Windows':
            asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())

        # 创建新的事件循环
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        # 在新的事件循环中运行查询
        result = loop.run_until_complete(query_id_card(card_id, verbose=False))
        return result

    except Exception as e:
        print(f"[{card_id}] 处理异常: {str(e)}")
        return False
    finally:
        # 安全地清理事件循环
        if loop is not None:
            try:
                # 取消所有挂起的任务
                pending = asyncio.all_tasks(loop)
                if pending:
                    for task in pending:
                        task.cancel()

                    # 等待任务取消完成
                    try:
                        loop.run_until_complete(asyncio.gather(*pending, return_exceptions=True))
                    except Exception:
                        pass  # 忽略取消任务时的异常

                # 关闭异步生成器
                try:
                    loop.run_until_complete(loop.shutdown_asyncgens())
                except Exception:
                    pass  # 忽略关闭异步生成器时的异常

                # 关闭事件循环
                if not loop.is_closed():
                    loop.close()

            except Exception:
                pass  # 忽略清理过程中的任何异常
            finally:
                # 清除线程局部存储的事件循环引用
                try:
                    asyncio.set_event_loop(None)
                except Exception:
                    pass

async def main():
    """
    示例使用函数
    """
    try:
        # 示例使用
        card_id = "46900719970409763X"  # 这里替换为实际的身份证号
        return await query_id_card(card_id)
    except Exception as e:
        print(f"主函数异常: {str(e)}")
        return False

# 添加一个简化的异步处理函数
async def process_batch_async(card_ids, batch_size=50):
    """
    异步批量处理身份证号
    """
    total = len(card_ids)
    completed = 0

    # 分批处理
    for i in range(0, total, batch_size):
        batch = card_ids[i:i + batch_size]

        # 创建任务列表
        tasks = [query_id_card(card_id, verbose=False) for card_id in batch]

        # 并发执行当前批次
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # 更新进度
        completed += len(batch)
        if completed % 100 == 0 or completed == total:
            print(f"已完成{completed}/{total}条")

    # 处理剩余的缓存结果
    with cache_lock:
        if results_cache:
            with file_lock:
                with open('Okey.txt', 'a', encoding='utf-8') as f:
                    for result_json in results_cache:
                        f.write(result_json + "\n")
                results_cache.clear()

# 修改为支持多线程处理的main函数
def main_multi_thread():
    """
    主函数: 使用多线程处理多个身份证号
    """
    # 为主线程设置合适的事件循环策略
    if platform.system() == 'Windows':
        asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())

    try:
        # 从文件读取身份证号列表
        card_ids = read_card_ids('1.txt')

        if not card_ids:
            print("未读取到有效的身份证号，程序退出")
            return

        # 清空或创建结果文件
        with open('Okey.txt', 'w', encoding='utf-8') as f:
            pass

        # 清空或创建错误文件
        with open('err.txt', 'w', encoding='utf-8') as f:
            pass

        # 设置线程池大小 (可根据实际情况调整)
        max_workers = min(16, os.cpu_count() * 2)  # 减少线程数避免资源竞争
        print(f"线程池：{max_workers}")
        print(f"配置{len(card_ids)}条成功 准备启动")

        # 使用线程池处理所有身份证号
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有任务到线程池
            futures = {executor.submit(process_card_id, card_id): card_id for card_id in card_ids}

            # 处理完成的任务结果
            completed = 0
            total = len(card_ids)
            for future in concurrent.futures.as_completed(futures):
                card_id = futures[future]
                try:
                    future.result()
                except Exception as e:
                    print(f"[{card_id}] 线程执行异常: {str(e)}")

                # 更新进度
                completed += 1
                # 每100条打印一次进度
                if completed % 100 == 0 or completed == total:
                    print(f"已完成{completed}条")

        # 处理剩余的缓存结果
        with cache_lock:
            if results_cache:
                with file_lock:
                    with open('Okey.txt', 'a', encoding='utf-8') as f:
                        for result_json in results_cache:
                            f.write(result_json + "\n")
                    results_cache.clear()

        print("所有身份证号处理完成")
    except Exception as e:
        print(f"主函数异常: {str(e)}")

# 添加一个纯异步的主函数作为替代方案
def main_async():
    """
    主函数: 使用纯异步处理多个身份证号（推荐使用）
    """
    try:
        # 从文件读取身份证号列表
        card_ids = read_card_ids('1.txt')

        if not card_ids:
            print("未读取到有效的身份证号，程序退出")
            return

        # 清空或创建结果文件
        with open('Okey.txt', 'w', encoding='utf-8') as f:
            pass

        # 清空或创建错误文件
        with open('err.txt', 'w', encoding='utf-8') as f:
            pass

        print(f"配置{len(card_ids)}条成功 准备启动")

        # 运行异步批量处理
        asyncio.run(process_batch_async(card_ids))

        print("所有身份证号处理完成")
    except Exception as e:
        print(f"主函数异常: {str(e)}")

if __name__ == '__main__':
    # 推荐使用纯异步版本，避免多线程中的事件循环问题
    main_async()

    # 如果需要使用多线程版本，请取消下面的注释并注释上面的main_async()
    # main_multi_thread()