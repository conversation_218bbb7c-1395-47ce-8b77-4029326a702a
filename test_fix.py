#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的query.py脚本
验证事件循环错误是否得到解决
"""

import os
import sys
import time
import subprocess

def create_test_data():
    """创建测试用的身份证号文件"""
    test_ids = [
        "46900719970409763X",
        "460007199704097630",
        "460007199704097631", 
        "460007199704097632",
        "460007199704097633"
    ]
    
    with open('1.txt', 'w') as f:
        for test_id in test_ids:
            f.write(test_id + '\n')
    
    print(f"已创建测试文件 1.txt，包含 {len(test_ids)} 条测试数据")

def run_test():
    """运行测试"""
    print("开始测试修复后的代码...")
    
    # 创建测试数据
    create_test_data()
    
    # 运行修复后的脚本
    try:
        result = subprocess.run([sys.executable, 'query.py'], 
                              capture_output=True, 
                              text=True, 
                              timeout=60)
        
        print("=== 标准输出 ===")
        print(result.stdout)
        
        if result.stderr:
            print("=== 错误输出 ===")
            print(result.stderr)
            
        print(f"=== 退出代码: {result.returncode} ===")
        
        # 检查是否还有事件循环错误
        if "_UnixSelectorEventLoop" in result.stderr or "BaseEventLoop" in result.stderr:
            print("❌ 仍然存在事件循环错误")
            return False
        else:
            print("✅ 未发现事件循环错误")
            
        # 检查输出文件
        if os.path.exists('Okey.txt'):
            with open('Okey.txt', 'r', encoding='utf-8') as f:
                content = f.read()
                if content.strip():
                    print(f"✅ 成功生成结果文件，大小: {len(content)} 字符")
                else:
                    print("⚠️ 结果文件为空")
        
        return True
        
    except subprocess.TimeoutExpired:
        print("❌ 测试超时")
        return False
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

def main():
    """主函数"""
    print("事件循环错误修复测试工具")
    print("=" * 50)
    
    if run_test():
        print("\n🎉 测试完成！修复效果良好。")
        print("\n建议:")
        print("1. 如果仍有少量警告，属于正常现象")
        print("2. 关注程序是否能正常完成处理")
        print("3. 检查输出文件是否正确生成")
    else:
        print("\n❌ 测试失败，可能需要进一步调整")

if __name__ == '__main__':
    main()
